.storage-comparison {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
}

.page-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* Controls Section */
.controls-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.controls-row {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.controls-row:last-child {
    margin-bottom: 0;
}

.search-box {
    flex: 1;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.sort-controls {
    display: flex;
    gap: 12px;
}

.sort-select {
    padding: 10px 14px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    cursor: pointer;
}

.sort-select:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.folder-path-setting {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.folder-path-setting label {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

.folder-path-input {
    padding: 10px 14px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    width: 250px;
    transition: all 0.2s ease;
    font-family: 'Courier New', monospace;
}

.folder-path-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Overall Statistics */
.overall-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #2c3e50;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #dee2e6;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.stats-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.overall-stats h3 {
    margin: 0 0 20px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.stat-value.error {
    color: #dc3545;
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-details-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    padding: 2px 6px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.error-details-btn:hover {
    background: rgba(220, 53, 69, 0.1);
}

/* Users Table */
.users-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.users-table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.users-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.users-table tr:hover {
    background: #f8f9fa;
}

.email-cell {
    min-width: 200px;
}

.email-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.email {
    font-weight: 500;
    color: #2c3e50;
}

.error-indicator {
    font-size: 16px;
    cursor: help;
}

.last-scanned {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.downloaded-cell {
    min-width: 120px;
}

.scanned-cell {
    min-width: 120px;
}

.scan-percentage-cell {
    min-width: 80px;
    text-align: center;
    font-weight: 600;
    color: #495057;
}

.missing-scan-cell {
    min-width: 120px;
    color: #dc3545;
    font-weight: 500;
}

.folder-path {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
    word-break: break-all;
}

.missing-bytes {
    color: #dc3545;
    font-weight: 500;
}

.complete {
    color: #28a745;
    font-weight: 500;
}

/* Progress Bar */
.progress-container {
    position: relative;
    width: 100px;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 500;
    color: #2c3e50;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
}

.btn-primary {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #545b62 0%, #3d4449 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-info:hover:not(:disabled) {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    background: #6c757d;
    color: white;
}

.btn-small:hover:not(:disabled) {
    background: #545b62;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.page-info {
    font-size: 14px;
    color: #6c757d;
}

/* Loading and Error States */
.loading {
    text-align: center;
    padding: 40px;
    font-size: 16px;
    color: #6c757d;
}

.error-message {
    text-align: center;
    padding: 40px;
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    margin: 20px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .users-table {
        font-size: 12px;
    }

    .users-table th,
    .users-table td {
        padding: 8px;
    }
}

@media (max-width: 768px) {
    .storage-comparison {
        padding: 10px;
    }

    .controls-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .search-box {
        max-width: none;
    }

    .sort-controls {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .users-table-container {
        overflow-x: auto;
    }

    .users-table {
        min-width: 800px;
    }

    .pagination {
        flex-direction: column;
        gap: 10px;
    }
}