import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import { formatBytes, formatPercentage } from '../utils/formatUtils';
import { useToast } from './Toast';
import './StorageDiscrepancyModal.css';

const StorageDiscrepancyModal = ({ isOpen, onClose, defaultFolderPath = 'E:\\' }) => {
    const [analysis, setAnalysis] = useState(null);
    const [loading, setLoading] = useState(false);
    const [folderPath, setFolderPath] = useState(defaultFolderPath);
    const [expandedUser, setExpandedUser] = useState(null);
    const [filterType, setFilterType] = useState('all'); // all, significant, missing, extra
    const { showError, showSuccess } = useToast();

    useEffect(() => {
        if (isOpen) {
            loadDiscrepancyData();
        }
    }, [isOpen, folderPath]);

    const loadDiscrepancyData = async () => {
        setLoading(true);
        try {
            const response = await apiGet(`/api/storage/analyze-discrepancies?folderPath=${encodeURIComponent(folderPath)}`);
            setAnalysis(response.analysis);
            showSuccess('Phân tích chênh lệch dung lượng hoàn thành');
        } catch (error) {
            console.error('Error loading discrepancy analysis:', error);
            showError('Không thể tải dữ liệu phân tích chênh lệch');
        } finally {
            setLoading(false);
        }
    };

    const toggleUserDetails = (userEmail) => {
        setExpandedUser(expandedUser === userEmail ? null : userEmail);
    };

    const getFilteredUsers = () => {
        if (!analysis) return [];
        
        switch (filterType) {
            case 'significant':
                return analysis.userDiscrepancies.filter(user => user.has_significant_discrepancy);
            case 'missing':
                return analysis.userDiscrepancies.filter(user => user.discrepancy_bytes < 0);
            case 'extra':
                return analysis.userDiscrepancies.filter(user => user.discrepancy_bytes > 0);
            default:
                return analysis.userDiscrepancies;
        }
    };

    const getDiscrepancyColor = (discrepancy) => {
        if (discrepancy < 0) return '#dc3545'; // Red for missing
        if (discrepancy > 0) return '#28a745'; // Green for extra
        return '#6c757d'; // Gray for no discrepancy
    };

    const getDiscrepancyIcon = (user) => {
        if (!user.folder_exists) return '❌';
        if (user.discrepancy_bytes < 0) return '⬇️';
        if (user.discrepancy_bytes > 0) return '⬆️';
        return '✅';
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content storage-discrepancy-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Phân tích chênh lệch dung lượng Download</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>

                <div className="modal-body">
                    <div className="folder-path-section">
                        <label htmlFor="folderPath">Đường dẫn thư mục:</label>
                        <div className="folder-path-input">
                            <input
                                id="folderPath"
                                type="text"
                                value={folderPath}
                                onChange={(e) => setFolderPath(e.target.value)}
                                placeholder="E:\"
                            />
                            <button 
                                className="btn btn-primary"
                                onClick={loadDiscrepancyData}
                                disabled={loading}
                            >
                                Phân tích
                            </button>
                        </div>
                    </div>

                    {loading ? (
                        <div className="loading-state">
                            <div className="spinner"></div>
                            <p>Đang phân tích chênh lệch dung lượng...</p>
                        </div>
                    ) : analysis ? (
                        <>
                            <div className="analysis-summary">
                                <h3>Tổng quan chênh lệch</h3>
                                <div className="summary-grid">
                                    <div className="summary-item">
                                        <span className="label">Tổng users:</span>
                                        <span className="value">{analysis.totalUsers}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">DB Downloaded:</span>
                                        <span className="value">{formatBytes(analysis.totalDbDownloaded)}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Thực tế Downloaded:</span>
                                        <span className="value">{formatBytes(analysis.totalActualDownloaded)}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Chênh lệch tổng:</span>
                                        <span 
                                            className="value"
                                            style={{ color: getDiscrepancyColor(analysis.totalDiscrepancy) }}
                                        >
                                            {formatBytes(Math.abs(analysis.totalDiscrepancy))}
                                            {analysis.totalDiscrepancy < 0 ? ' (thiếu)' : analysis.totalDiscrepancy > 0 ? ' (thừa)' : ''}
                                        </span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Users có chênh lệch:</span>
                                        <span className="value">{analysis.summary.usersWithDiscrepancies}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Tổng thiếu:</span>
                                        <span className="value missing">{formatBytes(analysis.summary.totalMissingBytes)}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Tổng thừa:</span>
                                        <span className="value extra">{formatBytes(analysis.summary.totalExtraBytes)}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Chênh lệch TB:</span>
                                        <span className="value">{formatBytes(Math.abs(analysis.summary.averageDiscrepancy))}</span>
                                    </div>
                                </div>
                            </div>

                            <div className="filter-section">
                                <label>Lọc theo:</label>
                                <select 
                                    value={filterType} 
                                    onChange={(e) => setFilterType(e.target.value)}
                                    className="filter-select"
                                >
                                    <option value="all">Tất cả users ({analysis.userDiscrepancies.length})</option>
                                    <option value="significant">Chênh lệch đáng kể ({analysis.summary.usersWithDiscrepancies})</option>
                                    <option value="missing">Thiếu dung lượng ({analysis.userDiscrepancies.filter(u => u.discrepancy_bytes < 0).length})</option>
                                    <option value="extra">Thừa dung lượng ({analysis.userDiscrepancies.filter(u => u.discrepancy_bytes > 0).length})</option>
                                </select>
                            </div>

                            <div className="users-discrepancy-list">
                                <h3>Chi tiết từng user</h3>
                                {getFilteredUsers().length === 0 ? (
                                    <p className="no-data">Không có dữ liệu phù hợp với bộ lọc</p>
                                ) : (
                                    getFilteredUsers().map((user) => {
                                        const isExpanded = expandedUser === user.user_email;
                                        
                                        return (
                                            <div key={user.user_email} className="user-discrepancy-item">
                                                <div 
                                                    className="user-discrepancy-header"
                                                    onClick={() => toggleUserDetails(user.user_email)}
                                                >
                                                    <div className="user-info">
                                                        <span className="discrepancy-icon">
                                                            {getDiscrepancyIcon(user)}
                                                        </span>
                                                        <span className="user-email">{user.user_email}</span>
                                                        {user.error && (
                                                            <span className="error-badge" title={user.error}>
                                                                ERROR
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="discrepancy-summary">
                                                        <span 
                                                            className="discrepancy-amount"
                                                            style={{ color: getDiscrepancyColor(user.discrepancy_bytes) }}
                                                        >
                                                            {formatBytes(Math.abs(user.discrepancy_bytes))}
                                                            {user.discrepancy_bytes !== 0 && (
                                                                <span className="discrepancy-percentage">
                                                                    ({formatPercentage(Math.abs(user.discrepancy_percentage))})
                                                                </span>
                                                            )}
                                                        </span>
                                                        <span className="expand-icon">
                                                            {isExpanded ? '▼' : '▶'}
                                                        </span>
                                                    </div>
                                                </div>

                                                {isExpanded && (
                                                    <div className="user-discrepancy-details">
                                                        <div className="details-grid">
                                                            <div className="detail-item">
                                                                <span>DB Downloaded:</span>
                                                                <span>{formatBytes(user.db_downloaded_bytes)}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Thực tế Downloaded:</span>
                                                                <span>{formatBytes(user.actual_downloaded_bytes)}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Drive Usage:</span>
                                                                <span>{formatBytes(user.drive_usage_bytes)}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Scanned Storage:</span>
                                                                <span>{formatBytes(user.scanned_storage_bytes)}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Folder tồn tại:</span>
                                                                <span>{user.folder_exists ? 'Có' : 'Không'}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Chênh lệch đáng kể:</span>
                                                                <span>{user.has_significant_discrepancy ? 'Có' : 'Không'}</span>
                                                            </div>
                                                        </div>
                                                        
                                                        {user.scan_error_message && (
                                                            <div className="scan-error">
                                                                <h4>Lỗi scan:</h4>
                                                                <pre>{user.scan_error_message}</pre>
                                                            </div>
                                                        )}
                                                        
                                                        {user.error && (
                                                            <div className="analysis-error">
                                                                <h4>Lỗi phân tích:</h4>
                                                                <pre>{user.error}</pre>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })
                                )}
                            </div>
                        </>
                    ) : (
                        <div className="no-analysis">
                            <p>Nhấn "Phân tích" để bắt đầu phân tích chênh lệch dung lượng</p>
                        </div>
                    )}
                </div>

                <div className="modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>
                        Đóng
                    </button>
                    {analysis && (
                        <button 
                            className="btn btn-primary" 
                            onClick={loadDiscrepancyData} 
                            disabled={loading}
                        >
                            Làm mới
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default StorageDiscrepancyModal;
