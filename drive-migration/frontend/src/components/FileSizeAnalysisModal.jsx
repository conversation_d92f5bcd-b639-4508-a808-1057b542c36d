import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import { formatBytes, formatPercentage } from '../utils/formatUtils';
import { useToast } from './Toast';
import './FileSizeAnalysisModal.css';

const FileSizeAnalysisModal = ({ isOpen, onClose }) => {
    const [analysis, setAnalysis] = useState(null);
    const [loading, setLoading] = useState(false);
    const [userEmail, setUserEmail] = useState('');
    const [limit, setLimit] = useState(100);
    const [filterType, setFilterType] = useState('all'); // all, google-docs, binary, size-increase
    const [expandedFile, setExpandedFile] = useState(null);
    const { showError, showSuccess } = useToast();

    useEffect(() => {
        if (isOpen) {
            loadAnalysisData();
        }
    }, [isOpen]);

    const loadAnalysisData = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                limit: limit.toString()
            });
            
            if (userEmail.trim()) {
                params.append('userEmail', userEmail.trim());
            }

            const response = await apiGet(`/api/storage/analyze-file-sizes?${params}`);
            setAnalysis(response.analysis);
            showSuccess('Phân tích file size hoàn thành');
        } catch (error) {
            console.error('Error loading file size analysis:', error);
            showError('Không thể tải dữ liệu phân tích file size');
        } finally {
            setLoading(false);
        }
    };

    const toggleFileDetails = (fileId) => {
        setExpandedFile(expandedFile === fileId ? null : fileId);
    };

    const getFilteredFiles = () => {
        if (!analysis) return [];
        
        switch (filterType) {
            case 'google-docs':
                return analysis.googleDocsFiles;
            case 'binary':
                return analysis.binaryFiles;
            case 'size-increase':
                return analysis.sizeDiscrepancies.filter(f => f.size_discrepancy > 0);
            case 'size-decrease':
                return analysis.sizeDiscrepancies.filter(f => f.size_discrepancy < 0);
            default:
                return [...analysis.googleDocsFiles, ...analysis.binaryFiles];
        }
    };

    const getSizeDiscrepancyColor = (discrepancy) => {
        if (discrepancy > 0) return '#28a745'; // Green for increase
        if (discrepancy < 0) return '#dc3545'; // Red for decrease
        return '#6c757d'; // Gray for no change
    };

    const getSizeDiscrepancyIcon = (file) => {
        if (!file.has_local_file) return '❓';
        if (file.size_discrepancy > 0) return '⬆️';
        if (file.size_discrepancy < 0) return '⬇️';
        return '➡️';
    };

    const getFileTypeIcon = (fileType) => {
        const icons = {
            'application/vnd.google-apps.document': '📄',
            'application/vnd.google-apps.spreadsheet': '📊',
            'application/vnd.google-apps.presentation': '📽️',
            'application/vnd.google-apps.drawing': '🎨',
            'application/pdf': '📕',
            'image/': '🖼️',
            'video/': '🎥',
            'audio/': '🎵'
        };

        for (const [type, icon] of Object.entries(icons)) {
            if (fileType.startsWith(type)) return icon;
        }
        return '📁';
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content file-size-analysis-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Phân tích File Size - Google Drive vs Downloaded</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>

                <div className="modal-body">
                    <div className="analysis-controls">
                        <div className="control-row">
                            <div className="control-group">
                                <label>User Email (tùy chọn):</label>
                                <input
                                    type="text"
                                    value={userEmail}
                                    onChange={(e) => setUserEmail(e.target.value)}
                                    placeholder="Để trống để phân tích tất cả users"
                                />
                            </div>
                            <div className="control-group">
                                <label>Số lượng files:</label>
                                <select value={limit} onChange={(e) => setLimit(parseInt(e.target.value))}>
                                    <option value={50}>50 files</option>
                                    <option value={100}>100 files</option>
                                    <option value={200}>200 files</option>
                                    <option value={500}>500 files</option>
                                </select>
                            </div>
                            <button 
                                className="btn btn-primary"
                                onClick={loadAnalysisData}
                                disabled={loading}
                            >
                                Phân tích
                            </button>
                        </div>
                    </div>

                    {loading ? (
                        <div className="loading-state">
                            <div className="spinner"></div>
                            <p>Đang phân tích file sizes...</p>
                        </div>
                    ) : analysis ? (
                        <>
                            <div className="analysis-summary">
                                <h3>Tổng quan phân tích</h3>
                                <div className="summary-grid">
                                    <div className="summary-item">
                                        <span className="label">Tổng files:</span>
                                        <span className="value">{analysis.totalFiles}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Google Docs:</span>
                                        <span className="value">{analysis.summary.totalGoogleDocs}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Binary files:</span>
                                        <span className="value">{analysis.summary.totalBinaryFiles}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Files tăng size:</span>
                                        <span className="value increase">{analysis.summary.filesWithSizeIncrease}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Files giảm size:</span>
                                        <span className="value decrease">{analysis.summary.filesWithSizeDecrease}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Tăng size TB:</span>
                                        <span className="value">{formatBytes(Math.abs(analysis.summary.averageSizeIncrease))}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">Tăng size lớn nhất:</span>
                                        <span className="value">{formatBytes(analysis.summary.largestSizeIncrease)}</span>
                                    </div>
                                    <div className="summary-item">
                                        <span className="label">File tăng nhiều nhất:</span>
                                        <span className="value" title={analysis.summary.largestSizeIncreaseFile}>
                                            {analysis.summary.largestSizeIncreaseFile ? 
                                                (analysis.summary.largestSizeIncreaseFile.length > 30 ? 
                                                    analysis.summary.largestSizeIncreaseFile.substring(0, 30) + '...' : 
                                                    analysis.summary.largestSizeIncreaseFile) : 
                                                'N/A'}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div className="conversion-stats">
                                <h3>Thống kê conversion Google Docs</h3>
                                <div className="conversion-grid">
                                    {Object.entries(analysis.summary.conversionStats).map(([mimeType, stats]) => (
                                        <div key={mimeType} className="conversion-item">
                                            <div className="conversion-header">
                                                <span className="file-icon">{getFileTypeIcon(mimeType)}</span>
                                                <span className="conversion-type">
                                                    {mimeType.includes('document') ? 'Google Docs' :
                                                     mimeType.includes('spreadsheet') ? 'Google Sheets' :
                                                     mimeType.includes('presentation') ? 'Google Slides' :
                                                     mimeType.includes('drawing') ? 'Google Drawings' : 'Unknown'}
                                                </span>
                                            </div>
                                            <div className="conversion-stats-details">
                                                <div>Số lượng: {stats.count}</div>
                                                <div>Tăng size TB: {formatBytes(Math.abs(stats.avgSizeIncrease))}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="filter-section">
                                <label>Lọc files:</label>
                                <select 
                                    value={filterType} 
                                    onChange={(e) => setFilterType(e.target.value)}
                                    className="filter-select"
                                >
                                    <option value="all">Tất cả files ({analysis.totalFiles})</option>
                                    <option value="google-docs">Google Docs ({analysis.summary.totalGoogleDocs})</option>
                                    <option value="binary">Binary files ({analysis.summary.totalBinaryFiles})</option>
                                    <option value="size-increase">Files tăng size ({analysis.summary.filesWithSizeIncrease})</option>
                                    <option value="size-decrease">Files giảm size ({analysis.summary.filesWithSizeDecrease})</option>
                                </select>
                            </div>

                            <div className="files-list">
                                <h3>Chi tiết files</h3>
                                {getFilteredFiles().length === 0 ? (
                                    <p className="no-data">Không có files phù hợp với bộ lọc</p>
                                ) : (
                                    getFilteredFiles().slice(0, 50).map((file) => {
                                        const isExpanded = expandedFile === file.file_id;
                                        
                                        return (
                                            <div key={file.file_id} className="file-item">
                                                <div 
                                                    className="file-header"
                                                    onClick={() => toggleFileDetails(file.file_id)}
                                                >
                                                    <div className="file-info">
                                                        <span className="file-icon">
                                                            {getFileTypeIcon(file.file_type)}
                                                        </span>
                                                        <span className="file-name" title={file.name}>
                                                            {file.name.length > 50 ? file.name.substring(0, 50) + '...' : file.name}
                                                        </span>
                                                        <span className="file-user">{file.user_email}</span>
                                                    </div>
                                                    <div className="size-info">
                                                        <span className="google-size">
                                                            Google: {formatBytes(file.google_size)}
                                                        </span>
                                                        {file.has_local_file && (
                                                            <>
                                                                <span className="actual-size">
                                                                    Local: {formatBytes(file.actual_size)}
                                                                </span>
                                                                <span 
                                                                    className="size-discrepancy"
                                                                    style={{ color: getSizeDiscrepancyColor(file.size_discrepancy) }}
                                                                >
                                                                    {getSizeDiscrepancyIcon(file)} {formatBytes(Math.abs(file.size_discrepancy))}
                                                                    ({formatPercentage(Math.abs(file.size_discrepancy_percentage))})
                                                                </span>
                                                            </>
                                                        )}
                                                        <span className="expand-icon">
                                                            {isExpanded ? '▼' : '▶'}
                                                        </span>
                                                    </div>
                                                </div>

                                                {isExpanded && (
                                                    <div className="file-details">
                                                        <div className="details-grid">
                                                            <div className="detail-item">
                                                                <span>File Type:</span>
                                                                <span>{file.file_type}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Is Google Doc:</span>
                                                                <span>{file.is_google_doc ? 'Yes' : 'No'}</span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Download Status:</span>
                                                                <span className={`status ${file.download_status}`}>
                                                                    {file.download_status || 'not_downloaded'}
                                                                </span>
                                                            </div>
                                                            <div className="detail-item">
                                                                <span>Has Local File:</span>
                                                                <span>{file.has_local_file ? 'Yes' : 'No'}</span>
                                                            </div>
                                                            {file.export_info && (
                                                                <>
                                                                    <div className="detail-item">
                                                                        <span>Export Format:</span>
                                                                        <span>{file.export_info.extension}</span>
                                                                    </div>
                                                                    <div className="detail-item">
                                                                        <span>Export Type:</span>
                                                                        <span>{file.export_info.name}</span>
                                                                    </div>
                                                                </>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })
                                )}
                                {getFilteredFiles().length > 50 && (
                                    <p className="more-files-note">
                                        Hiển thị 50/{getFilteredFiles().length} files đầu tiên
                                    </p>
                                )}
                            </div>
                        </>
                    ) : (
                        <div className="no-analysis">
                            <p>Nhấn "Phân tích" để bắt đầu phân tích file sizes</p>
                        </div>
                    )}
                </div>

                <div className="modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>
                        Đóng
                    </button>
                    {analysis && (
                        <button 
                            className="btn btn-primary" 
                            onClick={loadAnalysisData} 
                            disabled={loading}
                        >
                            Làm mới
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default FileSizeAnalysisModal;
